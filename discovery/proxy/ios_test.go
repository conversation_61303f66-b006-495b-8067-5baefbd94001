package proxy

import (
	"context"
	"log/slog"
	"net"
	"os"
	"testing"
	"time"
)

func TestIOSServer(t *testing.T) {
	// 测试创建服务器
	server := NewIOSServer(":0")
	if server == nil {
		t.Fatal("NewIOSServer返回nil")
	}

	// 测试默认值设置
	server.setDefaults()
	if server.USBMuxdSocket != DefaultUSBMuxdSocket {
		t.<PERSON><PERSON><PERSON>("期望USBMuxdSocket为%s，实际为%s", DefaultUSBMuxdSocket, server.USBMuxdSocket)
	}
	if server.ReadTimeout != DefaultReadTimeout {
		t.Errorf("期望ReadTimeout为%v，实际为%v", DefaultReadTimeout, server.ReadTimeout)
	}
	if server.WriteTimeout != DefaultWriteTimeout {
		t.Errorf("期望WriteTimeout为%v，实际为%v", DefaultWriteTimeout, server.WriteTimeout)
	}

	// 测试关闭未启动的服务器
	err := server.Close()
	if err != nil {
		t.Errorf("关闭未启动的服务器不应该返回错误: %v", err)
	}
}

func TestIOSServerListenAndServe(t *testing.T) {
	server := NewIOSServer(":0")

	// 测试在没有usbmuxd socket的情况下启动（在大多数测试环境中）
	// 我们期望这会快速失败
	done := make(chan error, 1)
	go func() {
		done <- server.ListenAndServe()
	}()

	select {
	case err := <-done:
		// 函数返回了，无论成功还是失败都是正常的
		t.Logf("ListenAndServe返回: %v", err)
	case <-time.After(100 * time.Millisecond):
		// 如果函数在100ms内没有返回，说明可能成功启动了
		// 这在有usbmuxd的系统上是正常的
		t.Log("ListenAndServe在100ms内没有返回，可能成功启动")
		server.Close() // 确保关闭服务器
	}
}

func TestStartIOSProxy(t *testing.T) {
	// 测试向后兼容的函数
	// 测试无效端口
	err := StartIOSProxy(-1, LogLevelOfDebug)
	if err == nil {
		t.Error("期望在无效端口时返回错误")
	}

	// 测试端口0（系统分配）- 这个测试会快速失败或成功
	// 在有usbmuxd的系统上会成功启动，在没有的系统上会失败
	// 我们主要测试函数不会panic
	done := make(chan error, 1)
	go func() {
		done <- StartIOSProxy(0, LogLevelOfDebug)
	}()

	select {
	case err := <-done:
		// 函数返回了，无论成功还是失败都是正常的
		t.Logf("StartIOSProxy返回: %v", err)
	case <-time.After(100 * time.Millisecond):
		// 如果函数在100ms内没有返回，说明可能成功启动了
		// 这在有usbmuxd的系统上是正常的
		t.Log("StartIOSProxy在100ms内没有返回，可能成功启动")
	}
}

func TestIOSServerHandleConnection(t *testing.T) {
	// 这个测试验证连接处理逻辑的基本结构
	// 由于handleConnection是内部方法，我们主要测试它不会panic

	// 创建一对连接用于测试
	serverConn, client := net.Pipe()
	defer serverConn.Close()
	defer client.Close()

	// 创建IOSServer实例
	server := NewIOSServer(":0")
	server.setDefaults()

	// 创建一个立即取消的上下文，这样函数会快速返回
	server.ctx, server.cancel = context.WithCancel(context.Background())
	server.cancel() // 立即取消

	// 设置ERROR级别以减少测试输出
	server.Logger = slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelError,
	}))

	// 这个测试主要验证函数不会panic
	// 由于上下文已取消，函数应该快速返回
	done := make(chan bool)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				t.Errorf("handleConnection发生panic: %v", r)
			}
			done <- true
		}()
		server.handleConnection(client)
	}()

	select {
	case <-done:
		// 函数正常返回
		t.Log("handleConnection正常返回")
	case <-time.After(50 * time.Millisecond):
		t.Error("handleConnection超时")
	}
}

func TestLogLevelIntegration(t *testing.T) {
	// 测试日志级别是否正确集成
	levels := []LogLevel{
		LogLevelOfDebug,
		LogLevelOfInfo,
		LogLevelOfWarn,
		LogLevelOfError,
	}

	for _, level := range levels {
		// 验证日志级别转换不会panic
		slogLevel := level.ToSLogLevel()
		if slogLevel.String() == "" {
			t.Errorf("日志级别 %s 转换失败", level)
		}
	}
}
