package proxy

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net"
	"os"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/proc"
)

const (
	DefaultUSBMuxdPort   = 27015
	DefaultUSBMuxdSocket = "/var/run/usbmuxd"
	DefaultReadTimeout   = 120 * time.Second
	DefaultWriteTimeout  = 120 * time.Second

	defaultBufferSize = 2 << 15 // 64KB
)

// IOSServer 类似于 http.Server，用于管理iOS代理服务
type IOSServer struct {
	*slog.Logger

	// Addr 指定监听的TCP地址，格式为 "host:port"
	// 如果为空，则使用 ":" + DefaultUSBMuxdPort
	Addr string

	// USBMuxdSocket 指定usbmuxd socket路径
	// 如果为空，则使用 DefaultUSBMuxdSocket
	USBMuxdSocket string

	// ReadTimeout 指定读取操作的超时时间
	// 如果为0，则使用 DefaultReadTimeout
	ReadTimeout time.Duration

	// WriteTimeout 指定写入操作的超时时间
	// 如果为0，则使用 DefaultWriteTimeout
	WriteTimeout time.Duration

	// 内部字段
	listener net.Listener
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
}

// StartIOSProxy 保持向后兼容的函数
func StartIOSProxy(port int, level LogLevel) error {
	if port <= 0 {
		port = DefaultUSBMuxdPort
	}

	server := &IOSServer{
		Logger: slog.New(
			slog.NewJSONHandler(
				os.Stdout, &slog.HandlerOptions{
					Level: level.ToSLogLevel(),
				},
			),
		),
		Addr: fmt.Sprintf(":%d", port),
	}
	return server.ListenAndServe()
}

// NewIOSServer 创建一个新的IOSServer实例
func NewIOSServer(addr string) *IOSServer {
	return &IOSServer{
		Addr: addr,
	}
}

// ListenAndServe 监听TCP地址并提供iOS代理服务
// 这个方法会阻塞直到服务器关闭或发生错误
func (s *IOSServer) ListenAndServe() (err error) {
	if s.listener != nil {
		return fmt.Errorf("服务器已在运行")
	}

	// 设置默认值
	s.setDefaults()

	// 检查usbmuxd socket是否存在
	if _, err = os.Stat(s.USBMuxdSocket); os.IsNotExist(err) {
		return fmt.Errorf("usbmuxd socket不存在: %s", s.USBMuxdSocket)
	}

	// 创建TCP监听器
	s.listener, err = net.Listen("tcp", s.Addr)
	if err != nil {
		return fmt.Errorf("监听地址失败，Addr: %s, error: %s", s.Addr, err)
	}

	return s.serve()
}

// Serve 在给定的监听器上提供iOS代理服务
func (s *IOSServer) Serve(listener net.Listener) error {
	if s.listener != nil {
		return fmt.Errorf("服务器已在运行")
	}

	s.listener = listener
	s.setDefaults()

	// 检查usbmuxd socket是否存在
	if _, err := os.Stat(s.USBMuxdSocket); os.IsNotExist(err) {
		return fmt.Errorf("usbmuxd socket不存在: %s", s.USBMuxdSocket)
	}

	return s.serve()
}

// Close 优雅地关闭服务器
func (s *IOSServer) Close() error {
	if s.cancel != nil {
		s.cancel()
	}
	if s.listener != nil {
		return s.listener.Close()
	}
	return nil
}

// Shutdown 优雅地关闭服务器，等待所有连接处理完成
func (s *IOSServer) Shutdown(ctx context.Context) error {
	if s.cancel != nil {
		s.cancel()
	}

	if s.listener != nil {
		_ = s.listener.Close()
	}

	// 等待所有连接处理完成或上下文超时
	done := make(chan lang.PlaceholderType)
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// setDefaults 设置默认值
func (s *IOSServer) setDefaults() {
	if s.USBMuxdSocket == "" {
		s.USBMuxdSocket = DefaultUSBMuxdSocket
	}
	if s.ReadTimeout == 0 {
		s.ReadTimeout = DefaultReadTimeout
	}
	if s.WriteTimeout == 0 {
		s.WriteTimeout = DefaultWriteTimeout
	}
	if s.Logger == nil {
		s.Logger = slog.New(
			slog.NewJSONHandler(
				os.Stdout, &slog.HandlerOptions{
					Level: slog.LevelError,
				},
			),
		)
	}
	if s.ctx == nil {
		s.ctx, s.cancel = context.WithCancel(context.Background())
	}
}

// serve 内部服务方法
func (s *IOSServer) serve() error {
	defer func() {
		if s.listener != nil {
			_ = s.listener.Close()
		}
	}()

	addr := s.listener.Addr().String()
	s.Info("iOS代理服务启动", "Addr", addr, "usbmuxd", s.USBMuxdSocket)

	proc.AddShutdownListener(
		func() {
			_ = s.Close()
		},
	)

	// 主循环：接受连接
	for {
		select {
		case <-s.ctx.Done():
			s.wg.Wait()
			return nil
		default:
			// 设置accept超时，以便能够响应ctx.Done()
			if tcpListener, ok := s.listener.(*net.TCPListener); ok {
				_ = tcpListener.SetDeadline(time.Now().Add(time.Second))
			}

			conn, err := s.listener.Accept()
			if err != nil {
				select {
				case <-s.ctx.Done():
					return nil
				default:
					var e net.Error
					if !errors.As(err, &e) || !e.Timeout() {
						s.Error("接受连接失败", "error", err)
					}
					continue
				}
			}

			s.wg.Add(1)
			go func(clientConn net.Conn) {
				defer s.wg.Done()
				s.handleConnection(clientConn)
			}(conn)
		}
	}
}

// handleConnection 处理单个客户端连接
func (s *IOSServer) handleConnection(clientConn net.Conn) {
	defer func() {
		if clientConn != nil {
			_ = clientConn.Close()
		}
	}()

	clientAddr := clientConn.RemoteAddr().String()

	// 连接到`usbmuxd socket`
	unixConn, err := net.Dial("unix", s.USBMuxdSocket)
	if err != nil {
		s.Error("连接usbmuxd失败", "client", clientAddr, "error", err)
		return
	}
	defer func() {
		if unixConn != nil {
			_ = unixConn.Close()
		}
	}()

	s.Debug("已连接到usbmuxd", "client", clientAddr)

	// 创建用于双向数据转发的上下文
	connCtx, connCancel := context.WithCancel(s.ctx)
	defer connCancel()

	var wg sync.WaitGroup

	// 客户端 -> usbmuxd
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer connCancel() // 任一方向断开时取消另一方向
		s.copyWithTimeout(connCtx, unixConn, clientConn, "client->usbmuxd", clientAddr)
	}()

	// usbmuxd -> 客户端
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer connCancel() // 任一方向断开时取消另一方向
		s.copyWithTimeout(connCtx, clientConn, unixConn, "usbmuxd->client", clientAddr)
	}()

	// 等待任一方向的数据传输完成
	wg.Wait()
	s.Debug("连接已关闭", "client", clientAddr)
}

// copyWithTimeout 在两个连接之间复制数据，支持超时和上下文取消
func (s *IOSServer) copyWithTimeout(ctx context.Context, dst, src net.Conn, direction, clientAddr string) {
	buffer := make([]byte, defaultBufferSize)

	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 设置读取超时
			if err := src.SetReadDeadline(time.Now().Add(s.ReadTimeout)); err != nil {
				s.Error("设置读取超时失败", "direction", direction, "client", clientAddr, "error", err)
				return
			}

			n, err := src.Read(buffer)
			if err != nil {
				if err == io.EOF {
					s.Debug("连接正常关闭", "direction", direction, "client", clientAddr)
				} else {
					var netErr net.Error
					if errors.As(err, &netErr) && netErr.Timeout() {
						s.Debug("读取超时", "direction", direction, "client", clientAddr)
					} else {
						s.Error("读取数据失败", "direction", direction, "client", clientAddr, "error", err)
					}
				}
				return
			}

			if n > 0 {
				// 设置写入超时
				if err = dst.SetWriteDeadline(time.Now().Add(s.WriteTimeout)); err != nil {
					s.Error("设置写入超时失败", "direction", direction, "client", clientAddr, "error", err)
					return
				}

				if _, err = dst.Write(buffer[:n]); err != nil {
					s.Error("写入数据失败", "direction", direction, "client", clientAddr, "error", err)
					return
				}

				s.Debug("数据转发", "direction", direction, "client", clientAddr, "bytes", n)
			}
		}
	}
}
