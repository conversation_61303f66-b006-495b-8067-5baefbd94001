package proxy

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"os"
	"time"
)

// ExampleIOSServer_basic 展示基本用法
func ExampleIOSServer_basic() {
	// 创建一个新的iOS代理服务器
	server := NewIOSServer(":27015")

	// 启动服务器（这会阻塞）
	if err := server.ListenAndServe(); err != nil {
		fmt.Printf("服务器启动失败: %v\n", err)
	}
}

// ExampleIOSServer_customized 展示自定义配置
func ExampleIOSServer_customized() {
	// 创建自定义logger
	logger := slog.New(
		slog.NewTextHandler(
			os.Stdout, &slog.HandlerOptions{
				Level: slog.LevelDebug,
			},
		),
	)

	// 创建服务器并设置自定义配置
	server := &IOSServer{
		Addr:          ":27015",
		USBMuxdSocket: "/var/run/usbmuxd",
		ReadTimeout:   60 * time.Second,
		WriteTimeout:  60 * time.Second,
		Logger:        logger,
	}

	// 启动服务器
	if err := server.ListenAndServe(); err != nil {
		fmt.Printf("服务器启动失败: %v\n", err)
	}
}

// ExampleIOSServer_gracefulShutdown 展示优雅关闭
func ExampleIOSServer_gracefulShutdown() {
	server := NewIOSServer(":27015")

	// 在goroutine中启动服务器
	go func() {
		if err := server.ListenAndServe(); err != nil {
			fmt.Printf("服务器启动失败: %v\n", err)
		}
	}()

	// 模拟运行一段时间
	time.Sleep(5 * time.Second)

	// 优雅关闭服务器，最多等待30秒
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		fmt.Printf("服务器关闭失败: %v\n", err)
	} else {
		fmt.Println("服务器已优雅关闭")
	}
}

// ExampleIOSServer_withCustomListener 展示使用自定义监听器
func ExampleIOSServer_withCustomListener() {
	// 创建自定义监听器
	listener, err := net.Listen("tcp", ":0") // 系统分配端口
	if err != nil {
		fmt.Printf("创建监听器失败: %v\n", err)
		return
	}

	fmt.Printf("监听地址: %s\n", listener.Addr().String())

	server := NewIOSServer("")

	// 使用自定义监听器启动服务器
	if err := server.Serve(listener); err != nil {
		fmt.Printf("服务器启动失败: %v\n", err)
	}
}
