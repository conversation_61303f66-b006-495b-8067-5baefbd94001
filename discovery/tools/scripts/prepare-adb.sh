#!/bin/bash

# prepare-adb.sh - 准备 adb 二进制文件用于嵌入
# 用法: ./prepare-adb.sh <target_os>
# 参数: target_os - 目标操作系统 (darwin, linux, windows)

set -e

# 检查参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <target_os>"
    echo "支持的目标操作系统: darwin, linux, windows"
    exit 1
fi

TARGET_OS="$1"

# 验证目标操作系统
case "$TARGET_OS" in
    darwin|linux|windows)
        ;;
    *)
        echo "错误: 不支持的目标操作系统 '$TARGET_OS'"
        echo "支持的目标操作系统: darwin, linux, windows"
        exit 1
        ;;
esac

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"
TOOLS_DIR="$SCRIPT_DIR/.."
ADB_EMBED_DIR="$TOOLS_DIR/internal/adb"

# 源文件路径
ADB_SOURCE_DIR="$PROJECT_ROOT/assets/adb"

# 根据目标操作系统选择对应的 adb 文件
case "$TARGET_OS" in
    darwin)
        ADB_SOURCE_FILE="$ADB_SOURCE_DIR/adb-darwin/adb"
        ;;
    linux)
        ADB_SOURCE_FILE="$ADB_SOURCE_DIR/adb-linux/adb"
        ;;
    windows)
        ADB_SOURCE_FILE="$ADB_SOURCE_DIR/adb-windows/adb.exe"
        ;;
esac

# 检查源文件是否存在
if [ ! -f "$ADB_SOURCE_FILE" ]; then
    echo "错误: 源文件不存在: $ADB_SOURCE_FILE"
    exit 1
fi

# 创建嵌入目录（如果不存在）
mkdir -p "$ADB_EMBED_DIR"

# 复制 adb 文件到嵌入目录
TARGET_FILE="$ADB_EMBED_DIR/adb-$TARGET_OS"
echo "复制 $ADB_SOURCE_FILE 到 $TARGET_FILE"
cp "$ADB_SOURCE_FILE" "$TARGET_FILE"

# 设置可执行权限
chmod +x "$TARGET_FILE"

echo "成功准备了 $TARGET_OS 平台的 adb 二进制文件"
