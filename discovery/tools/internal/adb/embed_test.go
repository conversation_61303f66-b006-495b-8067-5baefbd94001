package adb

import (
	"os"
	"os/exec"
	"runtime"
	"strings"
	"testing"
)

func TestEnsureADBInPath(t *testing.T) {
	// 保存原始的 PATH
	originalPath := os.Getenv("PATH")
	defer func() {
		os.Setenv("PATH", originalPath)
	}()

	// 清空 PATH 以确保 adb 不在其中
	os.Setenv("PATH", "")

	// 测试 EnsureADBInPath 函数
	err := EnsureADBInPath()
	if err != nil {
		t.Fatalf("EnsureADBInPath 失败: %v", err)
	}

	// 验证 adb 现在在 PATH 中
	_, err = exec.LookPath("adb")
	if err != nil {
		t.Fatalf("adb 不在 PATH 中: %v", err)
	}

	t.<PERSON>g("adb 成功添加到 PATH 中")
}

func TestGetADBFileName(t *testing.T) {
	fileName, err := getADBFileName()
	if err != nil {
		t.Fatalf("getADBFileName 失败: %v", err)
	}

	expectedPrefix := "adb-"
	if !strings.HasPrefix(fileName, expectedPrefix) {
		t.<PERSON><PERSON>("期望文件名以 %s 开头，实际为: %s", expectedPrefix, fileName)
	}

	// 验证返回的文件名与当前操作系统匹配
	switch runtime.GOOS {
	case "darwin":
		if fileName != "adb-darwin" {
			t.Errorf("在 darwin 系统上期望 adb-darwin，实际为: %s", fileName)
		}
	case "linux":
		if fileName != "adb-linux" {
			t.Errorf("在 linux 系统上期望 adb-linux，实际为: %s", fileName)
		}
	case "windows":
		if fileName != "adb-windows" {
			t.Errorf("在 windows 系统上期望 adb-windows，实际为: %s", fileName)
		}
	}

	t.Logf("当前系统 %s 的 adb 文件名: %s", runtime.GOOS, fileName)
}

func TestGetExecutableName(t *testing.T) {
	execName := getExecutableName()

	if runtime.GOOS == "windows" {
		if execName != "adb.exe" {
			t.Errorf("在 Windows 上期望 adb.exe，实际为: %s", execName)
		}
	} else {
		if execName != "adb" {
			t.Errorf("在非 Windows 系统上期望 adb，实际为: %s", execName)
		}
	}

	t.Logf("当前系统的可执行文件名: %s", execName)
}

func TestAddToPath(t *testing.T) {
	// 保存原始的 PATH
	originalPath := os.Getenv("PATH")
	defer func() {
		os.Setenv("PATH", originalPath)
	}()

	// 设置一个测试 PATH
	testPath := "/test/path"
	os.Setenv("PATH", testPath)

	// 添加新目录到 PATH
	newDir := "/new/test/dir"
	err := addToPath(newDir)
	if err != nil {
		t.Fatalf("addToPath 失败: %v", err)
	}

	// 验证新的 PATH
	newPath := os.Getenv("PATH")
	expectedSeparator := ":"
	if runtime.GOOS == "windows" {
		expectedSeparator = ";"
	}

	expectedPath := newDir + expectedSeparator + testPath
	if newPath != expectedPath {
		t.Errorf("期望 PATH 为 %s，实际为: %s", expectedPath, newPath)
	}

	t.Logf("成功添加目录到 PATH: %s", newPath)
}
