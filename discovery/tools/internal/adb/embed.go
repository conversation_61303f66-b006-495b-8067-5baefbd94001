package adb

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
)

// EnsureADBInPath 确保 adb 二进制文件在 PATH 中可用
// 它会将嵌入的 adb 二进制文件提取到临时目录，
// 并将该目录添加到 PATH 环境变量中
func EnsureADBInPath() error {
	// 检查 adb 是否已经在 PATH 中
	if isADBInPath() {
		return nil
	}

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "proxy-adb-*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %w", err)
	}

	// 提取 adb 二进制文件到临时目录
	adbPath := filepath.Join(tempDir, getExecutableName())
	if err := extractADBBinary(adbPath); err != nil {
		return fmt.Errorf("提取 adb 二进制文件失败: %w", err)
	}

	// 设置可执行权限
	if err := os.Chmod(adbPath, 0755); err != nil {
		return fmt.Errorf("设置 adb 可执行权限失败: %w", err)
	}

	// 将临时目录添加到 PATH
	if err := addToPath(tempDir); err != nil {
		return fmt.Errorf("添加到 PATH 失败: %w", err)
	}

	return nil
}

// isADBInPath 检查 adb 是否已经在 PATH 中可用
func isADBInPath() bool {
	// 尝试在 PATH 中查找 adb
	_, err := exec.LookPath("adb")
	return err == nil
}

// extractADBBinary 将嵌入的 adb 二进制文件写入到指定路径
func extractADBBinary(targetPath string) error {
	// 创建目标文件
	file, err := os.Create(targetPath)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %w", err)
	}
	defer file.Close()

	// 写入嵌入的二进制数据
	if _, err := file.Write(adbBinary); err != nil {
		return fmt.Errorf("写入 adb 文件失败: %w", err)
	}

	return nil
}

// addToPath 将指定目录添加到 PATH 环境变量
func addToPath(dir string) error {
	currentPath := os.Getenv("PATH")
	separator := ":"
	if runtime.GOOS == "windows" {
		separator = ";"
	}

	newPath := dir + separator + currentPath
	return os.Setenv("PATH", newPath)
}
