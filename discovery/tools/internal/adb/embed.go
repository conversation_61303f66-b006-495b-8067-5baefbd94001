package adb

import (
	"bytes"
	"embed"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
)

//go:embed adb-*
var adbBinaries embed.FS

// EnsureADBInPath 确保 adb 二进制文件在 PATH 中可用
// 它会根据当前运行的操作系统提取对应的 adb 二进制文件到临时目录，
// 并将该目录添加到 PATH 环境变量中
func EnsureADBInPath() error {
	// 获取当前操作系统对应的 adb 二进制文件名
	adbFileName, err := getADBFileName()
	if err != nil {
		return fmt.Errorf("获取 adb 文件名失败: %w", err)
	}

	// 检查 adb 是否已经在 PATH 中
	if isADBInPath() {
		return nil
	}

	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "proxy-adb-*")
	if err != nil {
		return fmt.Errorf("创建临时目录失败: %w", err)
	}

	// 提取 adb 二进制文件到临时目录
	adbPath := filepath.Join(tempDir, getExecutableName())
	if err := extractADBBinary(adbFileName, adbPath); err != nil {
		return fmt.Errorf("提取 adb 二进制文件失败: %w", err)
	}

	// 设置可执行权限
	if err := os.Chmod(adbPath, 0755); err != nil {
		return fmt.Errorf("设置 adb 可执行权限失败: %w", err)
	}

	// 将临时目录添加到 PATH
	if err := addToPath(tempDir); err != nil {
		return fmt.Errorf("添加到 PATH 失败: %w", err)
	}

	return nil
}

// getADBFileName 根据当前操作系统返回对应的 adb 二进制文件名
func getADBFileName() (string, error) {
	switch runtime.GOOS {
	case "darwin":
		return "adb-darwin", nil
	case "linux":
		return "adb-linux", nil
	case "windows":
		return "adb-windows", nil
	default:
		return "", fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}

// getExecutableName 根据操作系统返回可执行文件名
func getExecutableName() string {
	if runtime.GOOS == "windows" {
		return "adb.exe"
	}
	return "adb"
}

// isADBInPath 检查 adb 是否已经在 PATH 中可用
func isADBInPath() bool {
	// 尝试在 PATH 中查找 adb
	_, err := exec.LookPath("adb")
	return err == nil
}

// extractADBBinary 从嵌入的文件系统中提取 adb 二进制文件
func extractADBBinary(embeddedFileName, targetPath string) error {
	// 从嵌入的文件系统中读取文件
	data, err := adbBinaries.ReadFile(embeddedFileName)
	if err != nil {
		return fmt.Errorf("读取嵌入的 adb 文件失败: %w", err)
	}

	// 创建目标文件
	file, err := os.Create(targetPath)
	if err != nil {
		return fmt.Errorf("创建目标文件失败: %w", err)
	}
	defer file.Close()

	// 写入数据
	if _, err := io.Copy(file, bytes.NewReader(data)); err != nil {
		return fmt.Errorf("写入 adb 文件失败: %w", err)
	}

	return nil
}

// addToPath 将指定目录添加到 PATH 环境变量
func addToPath(dir string) error {
	currentPath := os.Getenv("PATH")
	separator := ":"
	if runtime.GOOS == "windows" {
		separator = ";"
	}

	newPath := dir + separator + currentPath
	return os.Setenv("PATH", newPath)
}
