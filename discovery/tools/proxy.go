package main

import (
	"fmt"
	"log"
	"strings"

	"github.com/spf13/cobra"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/version"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/assets/adb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/proxy"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/discovery/tools/cmd"
)

const (
	minPort = 1024
	maxPort = 65535
)

var (
	tp     = new(proxy.DeviceType)
	port   = new(int)
	device = new(string)
	level  = new(proxy.LogLevel)
)

func main() {
	// 初始化 adb 环境
	if err := adb.EnsureADBInPath(); err != nil {
		log.Printf("警告: 初始化 adb 环境失败: %v", err)
	}

	root := cmd.NewRootCommand()
	root.Args = cobra.NoArgs
	root.RunE = func(cmd *cobra.Command, args []string) error {
		if err := validateFlags(); err != nil {
			return err
		}

		switch *tp {
		case proxy.DeviceTypeOfAndroid:
			fmt.Printf("Connect with `adb connect ${当前主机IP}:%d`\n", *port)
			return proxy.StartAndroidProxy(*port, *device, *level)
		case proxy.DeviceTypeOfIOS:
			fmt.Printf("Connect with usbmuxd listen address, ${当前主机IP}:%d\n", *port)
			return proxy.StartIOSProxy(*port, *level)
		default:
			return fmt.Errorf("unknown device type: %s", *tp)
		}
	}

	vi := version.NewVersionInfo()
	root.Version = vi.String()

	flags := root.Flags()
	flags.SortFlags = false
	flags.VarP(tp, "type", "t", fmt.Sprintf("设备类型，可选值有：%s", strings.Join(tp.Options(), ", ")))
	flags.IntVarP(
		port, "port", "p", 0,
		fmt.Sprintf(
			"对外暴露的端口号，可选范围为：[%d, %d]；对于`iOS`默认端口号为：%d",
			minPort, maxPort, proxy.DefaultUSBMuxdPort,
		),
	)
	flags.StringVarP(device, "device", "d", "", "设备编号，对于`Android`必填，对于`iOS`无需填写")
	flags.VarP(level, "level", "l", fmt.Sprintf("日志级别，可选值有：%s", strings.Join(level.Options(), ", ")))

	cobra.CheckErr(root.Execute())
}

func validateFlags() error {
	switch *tp {
	case proxy.DeviceTypeOfAndroid:
		return validateAndroidFlags()
	case proxy.DeviceTypeOfIOS:
		return validateIOSFlags()
	default:
		return fmt.Errorf("未知的设备类型「%s」，可选值有: %s", *tp, strings.Join(tp.Options(), ", "))
	}
}

func validateAndroidFlags() error {
	if *port == 0 {
		return fmt.Errorf("对外暴露的端口号不能为空")
	} else if *port < minPort || *port > maxPort {
		return fmt.Errorf("对外暴露的端口号必须在%d和%d之间", minPort, maxPort)
	}

	if len(*device) == 0 {
		return fmt.Errorf("设备编号不能为空")
	}

	return nil
}

func validateIOSFlags() error {
	if *port == 0 {
		*port = proxy.DefaultUSBMuxdPort
	} else if *port != proxy.DefaultUSBMuxdPort {
		if *port < minPort || *port > maxPort {
			return fmt.Errorf("对外暴露的端口号必须在%d和%d之间", minPort, maxPort)
		}
		fmt.Printf("使用指定的端口号%d代替默认端口号%d\n", *port, proxy.DefaultUSBMuxdPort)
	}

	if len(*device) != 0 {
		fmt.Printf("对于iOS设备，设备编号将被忽略\n")
	}

	return nil
}
